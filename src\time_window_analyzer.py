"""
时间窗口分析模块
负责计算不同时间窗口内的板块表现
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import logging
import os
import sys
from collections import defaultdict
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

# 忽略pandas的警告
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

class TimeWindowAnalyzer:
    """时间窗口分析器类"""

    def __init__(self, data: pd.DataFrame):
        """
        初始化时间窗口分析器

        Args:
            data: 多级索引的DataFrame，索引为(date, sector_code)
        """
        self.data = data
        self.logger = self._setup_logger()
        self.cache = {}  # 缓存计算结果

        # 验证数据格式
        if not isinstance(data.index, pd.MultiIndex):
            raise ValueError("数据必须具有多级索引 (date, sector_code)")

        if 'date' not in data.index.names or 'sector_code' not in data.index.names:
            raise ValueError("索引必须包含 'date' 和 'sector_code'")

        if 'change_pct' not in data.columns:
            raise ValueError("数据必须包含 'change_pct' 列")

        # 获取可用的日期列表（已排序）
        self.available_dates = sorted(data.index.get_level_values('date').unique())
        self.date_range = (self.available_dates[0], self.available_dates[-1])

        # 为未来时间窗口分析准备过滤后的数据（仅2024年及之前）
        self.filtered_data_for_future = self._filter_data_for_future_analysis(data)
        if not self.filtered_data_for_future.empty:
            self.filtered_available_dates = sorted(
                self.filtered_data_for_future.index.get_level_values('date').unique()
            )
            self.filtered_date_range = (self.filtered_available_dates[0], self.filtered_available_dates[-1])
        else:
            self.filtered_available_dates = []
            self.filtered_date_range = (None, None)

        self.logger.info(f"时间窗口分析器初始化完成")
        self.logger.info(f"数据日期范围: {self.date_range[0]} 到 {self.date_range[1]}")
        self.logger.info(f"总交易日数: {len(self.available_dates)}")
        self.logger.info(f"未来分析数据日期范围: {self.filtered_date_range[0]} 到 {self.filtered_date_range[1]}")
        self.logger.info(f"未来分析交易日数: {len(self.filtered_available_dates)}")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _filter_data_for_future_analysis(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        为未来时间窗口分析过滤数据，仅保留2024年及之前的数据

        Args:
            data: 原始数据DataFrame

        Returns:
            过滤后的DataFrame
        """
        try:
            # 获取所有日期
            dates = data.index.get_level_values('date')

            # 过滤2024年及之前的数据
            max_year = config.DATA_YEAR_LIMIT['future_analysis_max_year']
            filtered_mask = dates.year <= max_year

            filtered_data = data[filtered_mask]

            self.logger.info(f"未来分析数据过滤完成: 保留 {len(filtered_data)} 条记录 (≤{max_year}年)")

            return filtered_data

        except Exception as e:
            self.logger.error(f"数据过滤失败: {str(e)}")
            return pd.DataFrame()

    def _get_trading_days_in_window(self, end_date: Union[str, pd.Timestamp], window_days: int) -> List[pd.Timestamp]:
        """
        获取时间窗口内的交易日

        Args:
            end_date: 结束日期
            window_days: 窗口天数

        Returns:
            时间窗口内的交易日列表
        """
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # 找到结束日期在可用日期中的位置
        available_dates_ts = pd.to_datetime(self.available_dates)

        # 如果指定的结束日期不在数据中，找到最近的交易日
        if end_date not in available_dates_ts:
            # 找到小于等于end_date的最大日期
            valid_dates = available_dates_ts[available_dates_ts <= end_date]
            if len(valid_dates) == 0:
                return []
            end_date = valid_dates.max()

        # 找到结束日期的索引
        end_idx = available_dates_ts.get_loc(end_date)

        # 计算开始索引（向前回退window_days个交易日）
        start_idx = max(0, end_idx - window_days + 1)

        # 返回时间窗口内的交易日
        return available_dates_ts[start_idx:end_idx + 1].tolist()

    def _get_future_trading_days_in_window(self, start_date: Union[str, pd.Timestamp], window_days: int) -> List[pd.Timestamp]:
        """
        获取未来时间窗口内的交易日（从指定日期向后推算）

        Args:
            start_date: 开始日期
            window_days: 窗口天数

        Returns:
            未来时间窗口内的交易日列表
        """
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)

        # 使用过滤后的数据（仅2024年及之前）
        if not self.filtered_available_dates:
            self.logger.warning("没有可用的过滤数据进行未来分析")
            return []

        available_dates_ts = pd.to_datetime(self.filtered_available_dates)

        # 如果指定的开始日期不在数据中，找到最近的交易日
        if start_date not in available_dates_ts:
            # 找到大于等于start_date的最小日期
            valid_dates = available_dates_ts[available_dates_ts >= start_date]
            if len(valid_dates) == 0:
                return []
            start_date = valid_dates.min()

        # 找到开始日期的索引
        start_idx = available_dates_ts.get_loc(start_date)

        # 计算结束索引（向后推进window_days个交易日）
        end_idx = min(len(available_dates_ts) - 1, start_idx + window_days - 1)

        # 检查是否有足够的未来数据
        actual_days = end_idx - start_idx + 1
        if actual_days < window_days:
            self.logger.warning(f"未来数据不足: 需要{window_days}天，实际只有{actual_days}天")

        # 返回未来时间窗口内的交易日
        return available_dates_ts[start_idx:end_idx + 1].tolist()

    def _find_closest_trading_day(self, target_date: pd.Timestamp) -> pd.Timestamp:
        """
        找到最接近目标日期的交易日

        Args:
            target_date: 目标日期

        Returns:
            最接近的交易日，如果没有找到则返回None
        """
        if not self.filtered_available_dates:
            return None

        available_dates_ts = pd.to_datetime(self.filtered_available_dates)

        # 找到最接近的日期
        differences = abs(available_dates_ts - target_date)
        min_diff_idx = differences.argmin()

        # 获取最小差异的天数（修复numpy.timedelta64问题）
        # 方法1: 使用pd.Timedelta除法
        try:
            differences_array = differences.values
            min_diff_timedelta = differences_array[min_diff_idx]
            min_diff_days = int(min_diff_timedelta / pd.Timedelta(days=1))
        except Exception:
            # 方法2: 转换为pandas Timedelta
            try:
                min_diff_timedelta = pd.Timedelta(differences.values[min_diff_idx])
                min_diff_days = min_diff_timedelta.days
            except Exception:
                # 方法3: 使用numpy除法
                min_diff_timedelta = differences.values[min_diff_idx]
                min_diff_days = int(min_diff_timedelta / np.timedelta64(1, 'D'))

        # 如果差异超过7天，认为没有合适的匹配
        if min_diff_days > 7:
            return None

        # 修复DatetimeIndex访问问题：使用直接索引而不是.iloc
        closest_trading_day = available_dates_ts[min_diff_idx]
        return closest_trading_day

    def calculate_window_performance(self, end_date: Union[str, pd.Timestamp], window_days: int) -> pd.DataFrame:
        """
        计算时间窗口内的板块表现

        Args:
            end_date: 结束日期
            window_days: 窗口天数

        Returns:
            包含板块表现的DataFrame
        """
        # 生成缓存键
        cache_key = f"{end_date}_{window_days}"
        if cache_key in self.cache:
            self.logger.debug(f"使用缓存结果: {cache_key}")
            return self.cache[cache_key]

        # 获取时间窗口内的交易日
        window_dates = self._get_trading_days_in_window(end_date, window_days)

        if not window_dates:
            self.logger.warning(f"没有找到有效的交易日: end_date={end_date}, window_days={window_days}")
            return pd.DataFrame()

        self.logger.info(f"计算时间窗口表现: {window_dates[0].strftime('%Y-%m-%d')} 到 {window_dates[-1].strftime('%Y-%m-%d')} ({len(window_dates)}个交易日)")

        # 筛选时间窗口内的数据
        window_data = self.data.loc[self.data.index.get_level_values('date').isin(window_dates)]

        if window_data.empty:
            self.logger.warning("时间窗口内没有数据")
            return pd.DataFrame()

        # 计算每个板块的累计涨跌幅
        performance_results = []

        for sector_code in window_data.index.get_level_values('sector_code').unique():
            try:
                sector_data = window_data.loc[window_data.index.get_level_values('sector_code') == sector_code]

                if sector_data.empty:
                    continue

                # 计算累计涨跌幅（复合增长）
                change_pcts = sector_data['change_pct'].values
                # 使用复合增长公式: (1 + r1) * (1 + r2) * ... - 1
                cumulative_return = np.prod(1 + change_pcts / 100) - 1
                cumulative_return_pct = cumulative_return * 100

                # 获取板块名称
                sector_name = sector_data['sector_name'].iloc[0] if 'sector_name' in sector_data.columns else sector_code

                # 计算其他统计指标
                avg_change = change_pcts.mean()
                volatility = change_pcts.std()
                max_change = change_pcts.max()
                min_change = change_pcts.min()
                trading_days = len(change_pcts)

                performance_results.append({
                    'sector_code': sector_code,
                    'sector_name': sector_name,
                    'cumulative_return_pct': cumulative_return_pct,
                    'avg_daily_change_pct': avg_change,
                    'volatility': volatility,
                    'max_daily_change_pct': max_change,
                    'min_daily_change_pct': min_change,
                    'trading_days': trading_days,
                    'window_start': window_dates[0],
                    'window_end': window_dates[-1],
                    'window_days': len(window_dates)
                })

            except Exception as e:
                self.logger.warning(f"计算板块 {sector_code} 表现时出错: {str(e)}")
                continue

        if not performance_results:
            self.logger.warning("没有成功计算任何板块的表现")
            return pd.DataFrame()

        # 转换为DataFrame并排序
        result_df = pd.DataFrame(performance_results)
        result_df = result_df.sort_values('cumulative_return_pct', ascending=False).reset_index(drop=True)

        # 缓存结果
        self.cache[cache_key] = result_df

        self.logger.info(f"成功计算 {len(result_df)} 个板块的时间窗口表现")
        return result_df

    def get_top_performers(self, window_data: pd.DataFrame, top_n: int = 10) -> pd.DataFrame:
        """
        获取表现最佳的板块

        Args:
            window_data: 时间窗口表现数据
            top_n: 返回前N名

        Returns:
            前N名板块的DataFrame
        """
        if window_data.empty:
            return pd.DataFrame()

        # 按累计涨跌幅排序，取前N名
        top_performers = window_data.head(top_n).copy()

        # 添加排名列
        top_performers['rank'] = range(1, len(top_performers) + 1)

        self.logger.info(f"获取前 {len(top_performers)} 名表现最佳的板块")
        return top_performers

    def calculate_multiple_windows(self, end_date: Union[str, pd.Timestamp],
                                 window_days_list: List[int]) -> Dict[int, pd.DataFrame]:
        """
        计算多个时间窗口的表现

        Args:
            end_date: 结束日期
            window_days_list: 时间窗口天数列表

        Returns:
            字典，键为窗口天数，值为表现DataFrame
        """
        results = {}

        for window_days in window_days_list:
            self.logger.info(f"计算 {window_days} 日时间窗口表现...")
            performance = self.calculate_window_performance(end_date, window_days)
            results[window_days] = performance

        return results

    def calculate_future_window_performance(self, start_date: Union[str, pd.Timestamp], window_days: int) -> pd.DataFrame:
        """
        计算未来时间窗口内的板块表现（基于历史同期数据分析）

        Args:
            start_date: 开始日期（参考日期）
            window_days: 窗口天数

        Returns:
            包含板块表现的DataFrame，基于历史同期数据
        """
        # 生成缓存键
        cache_key = f"future_{start_date}_{window_days}"
        if cache_key in self.cache:
            self.logger.debug(f"使用缓存结果: {cache_key}")
            return self.cache[cache_key]

        # 将开始日期转换为Timestamp
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)

        # 获取参考日期的月日
        reference_month_day = (start_date.month, start_date.day)

        # 计算目标时间窗口的结束日期（仅用于显示）
        # 注意：这只是一个近似值，因为实际交易日会受到周末和节假日影响
        approx_end_date = start_date + pd.Timedelta(days=window_days)

        self.logger.info(f"分析未来时间窗口: {start_date.strftime('%Y-%m-%d')} 到 约 {approx_end_date.strftime('%Y-%m-%d')} (目标{window_days}个交易日)")

        # 查找过去5年同期的数据
        historical_periods = []
        current_year = start_date.year

        # 确定要分析的年份范围（过去5年）
        analysis_years = list(range(current_year-5, current_year))
        self.logger.info(f"分析历史同期年份: {analysis_years}")

        # 对每个历史年份，找到对应的同期日期
        for year in analysis_years:
            # 创建该年对应的同期日期
            historical_date = pd.Timestamp(year=year, month=reference_month_day[0], day=reference_month_day[1])

            # 找到最接近的实际交易日
            closest_date = self._find_closest_trading_day(historical_date)

            if closest_date is not None:
                # 获取该日期开始的时间窗口
                window_dates = self._get_future_trading_days_in_window(closest_date, window_days)

                if len(window_dates) >= window_days * 0.8:  # 至少有80%的数据
                    historical_periods.append({
                        'year': year,
                        'start_date': closest_date,
                        'window_dates': window_dates,
                        'actual_days': len(window_dates)
                    })
                    self.logger.info(f"找到{year}年同期数据: {closest_date.strftime('%Y-%m-%d')} 开始，共{len(window_dates)}个交易日")

        if not historical_periods:
            self.logger.warning("没有找到有效的历史同期数据")
            return pd.DataFrame()

        # 合并所有历史同期的板块表现数据
        all_performance_results = []

        for period in historical_periods:
            # 筛选该时间窗口内的数据
            window_data = self.filtered_data_for_future.loc[
                self.filtered_data_for_future.index.get_level_values('date').isin(period['window_dates'])
            ]

            if window_data.empty:
                continue

            # 计算每个板块在该历史同期的表现
            for sector_code in window_data.index.get_level_values('sector_code').unique():
                try:
                    sector_data = window_data.loc[window_data.index.get_level_values('sector_code') == sector_code]

                    if sector_data.empty:
                        continue

                    # 按日期排序
                    sector_data = sector_data.sort_index(level='date')

                    # 计算累计收益率
                    daily_returns = sector_data['change_pct'].values / 100.0
                    cumulative_return = (1 + daily_returns).prod() - 1

                    # 获取板块信息
                    sector_info = sector_data.iloc[0]

                    all_performance_results.append({
                        'sector_code': sector_code,
                        'sector_name': sector_info.get('sector_name', sector_code),
                        'year': period['year'],
                        'cumulative_return_pct': cumulative_return * 100,
                        'trading_days': len(sector_data),
                        'start_date': period['start_date'],
                        'end_date': period['window_dates'][-1] if period['window_dates'] else period['start_date'],
                        'avg_daily_return_pct': (daily_returns.mean() * 100) if len(daily_returns) > 0 else 0,
                        'volatility_pct': (daily_returns.std() * 100) if len(daily_returns) > 1 else 0,
                        'max_daily_gain_pct': (daily_returns.max() * 100) if len(daily_returns) > 0 else 0,
                        'max_daily_loss_pct': (daily_returns.min() * 100) if len(daily_returns) > 0 else 0
                    })

                except Exception as e:
                    self.logger.debug(f"计算板块 {sector_code} 在{period['year']}年同期表现时出错: {str(e)}")
                    continue

        if not all_performance_results:
            self.logger.warning("没有计算出任何板块的历史同期表现")
            return pd.DataFrame()

        # 转换为DataFrame
        all_performance_df = pd.DataFrame(all_performance_results)

        # 按板块分组，计算历史同期的统计数据
        sector_stats = []

        for sector_code in all_performance_df['sector_code'].unique():
            sector_data = all_performance_df[all_performance_df['sector_code'] == sector_code]

            if len(sector_data) == 0:
                continue

            # 计算统计指标
            returns = sector_data['cumulative_return_pct'].values
            sector_name = sector_data['sector_name'].iloc[0]

            # 计算一致性指标
            positive_years = len(returns[returns > 0])
            total_years = len(returns)
            consistency_rate = positive_years / total_years if total_years > 0 else 0

            sector_stats.append({
                'sector_code': sector_code,
                'sector_name': sector_name,
                'avg_return_pct': returns.mean(),
                'median_return_pct': np.median(returns),
                'std_return_pct': returns.std(),
                'min_return_pct': returns.min(),
                'max_return_pct': returns.max(),
                'positive_years': positive_years,
                'total_years': total_years,
                'consistency_rate': consistency_rate,
                'consistency_percentage': consistency_rate * 100,
                'cumulative_return_pct': returns.mean(),  # 用平均值作为主要排序指标
                'trading_days': int(sector_data['trading_days'].mean()),
                'start_date': start_date,  # 使用参考日期
                'end_date': approx_end_date,  # 使用近似结束日期
                'historical_years': sorted(sector_data['year'].unique()),
                'avg_daily_return_pct': sector_data['avg_daily_return_pct'].mean(),
                'volatility_pct': sector_data['volatility_pct'].mean()
            })

        if not sector_stats:
            self.logger.warning("没有生成任何板块的统计数据")
            return pd.DataFrame()

        # 转换为DataFrame并按一致性排序（重点关注表现稳定的板块）
        performance_df = pd.DataFrame(sector_stats)
        # 首先按一致性率排序，然后按平均收益率排序
        performance_df = performance_df.sort_values(['consistency_rate', 'avg_return_pct'], ascending=[False, False])

        # 缓存结果
        self.cache[cache_key] = performance_df

        analyzed_years = sorted(set(all_performance_df['year'].unique()))
        self.logger.info(f"未来时间窗口分析完成（基于历史同期数据）:")
        self.logger.info(f"  - 分析年份: {analyzed_years}")
        self.logger.info(f"  - 板块数量: {len(performance_df)}")
        self.logger.info(f"  - 历史数据点: {len(all_performance_results)}")

        return performance_df

    def calculate_multiple_future_windows(self, start_date: Union[str, pd.Timestamp],
                                         window_days_list: List[int]) -> Dict[int, pd.DataFrame]:
        """
        计算多个未来时间窗口的表现（基于历史同期数据）

        Args:
            start_date: 参考日期
            window_days_list: 未来时间窗口天数列表

        Returns:
            字典，键为窗口天数，值为表现DataFrame
        """
        results = {}

        for window_days in window_days_list:
            self.logger.info(f"计算未来 {window_days} 日时间窗口表现（基于历史同期数据）...")
            performance = self.calculate_future_window_performance(start_date, window_days)
            results[window_days] = performance

            if not performance.empty:
                # 筛选一致性表现板块（一致性率>=50%）
                consistent_sectors = performance[performance['consistency_rate'] >= 0.5]
                if not consistent_sectors.empty:
                    top_consistent = consistent_sectors.head(3)
                    self.logger.info(f"  未来{window_days}日窗口一致性表现板块（前3名）:")
                    for i, (_, sector) in enumerate(top_consistent.iterrows(), 1):
                        self.logger.info(f"    {i}. {sector['sector_name']} - 一致性: {sector['consistency_percentage']:.1f}% "
                                        f"({sector['positive_years']}/{sector['total_years']}年), "
                                        f"平均收益: {sector['avg_return_pct']:.2f}%")
                else:
                    self.logger.info(f"  未来{window_days}日窗口：未找到一致性>=50%的板块")

        return results

    def calculate_combined_windows(self, reference_date: Union[str, pd.Timestamp],
                                 historical_windows: List[int] = None,
                                 future_windows: List[int] = None) -> Dict[str, Dict[int, pd.DataFrame]]:
        """
        计算历史和未来时间窗口的组合分析

        Args:
            reference_date: 参考日期
            historical_windows: 历史时间窗口列表，默认使用配置中的TIME_WINDOWS
            future_windows: 未来时间窗口列表，默认使用配置中的FUTURE_TIME_WINDOWS

        Returns:
            包含历史和未来分析结果的字典
        """
        if historical_windows is None:
            historical_windows = config.TIME_WINDOWS
        if future_windows is None:
            future_windows = config.FUTURE_TIME_WINDOWS

        results = {
            'historical': {},
            'future': {}
        }

        # 计算历史时间窗口（回望）
        self.logger.info("开始计算历史时间窗口分析...")
        results['historical'] = self.calculate_multiple_windows(reference_date, historical_windows)

        # 计算未来时间窗口（前瞻）
        self.logger.info("开始计算未来时间窗口分析...")
        results['future'] = self.calculate_multiple_future_windows(reference_date, future_windows)

        # 统计信息
        historical_count = sum(len(df) for df in results['historical'].values() if not df.empty)
        future_count = sum(len(df) for df in results['future'].values() if not df.empty)

        self.logger.info(f"组合分析完成: 历史分析{historical_count}条记录，未来分析{future_count}条记录")

        return results

    def compare_yearly_windows(self, window_days: int, target_date: Union[str, pd.Timestamp] = None) -> Dict:
        """
        跨年度时间窗口对比分析

        Args:
            window_days: 时间窗口天数
            target_date: 目标日期，默认使用最新日期

        Returns:
            包含跨年度对比结果的字典
        """
        if target_date is None:
            target_date = self.available_dates[-1]
        elif isinstance(target_date, str):
            target_date = pd.to_datetime(target_date)

        self.logger.info(f"开始跨年度 {window_days} 日时间窗口对比分析，目标日期: {target_date}")

        # 获取目标日期的月日
        target_month_day = (target_date.month, target_date.day)

        # 查找历年同期的日期
        yearly_dates = []
        for available_date in self.available_dates:
            date_ts = pd.to_datetime(available_date)
            # 检查是否为同期（月日相同或接近）
            if (date_ts.month, date_ts.day) == target_month_day:
                yearly_dates.append(date_ts)
            # 也包括前后几天的日期（处理周末和节假日）
            elif abs((date_ts - target_date.replace(year=date_ts.year)).days) <= 3:
                yearly_dates.append(date_ts)

        # 按年份分组
        yearly_results = {}
        yearly_performance = {}

        for date in yearly_dates:
            year = date.year
            if year not in yearly_results:
                performance = self.calculate_window_performance(date, window_days)
                if not performance.empty:
                    yearly_results[year] = date
                    yearly_performance[year] = performance

        if len(yearly_performance) < 2:
            self.logger.warning("找到的年份数据不足，无法进行有效对比")
            return {'error': '数据不足，无法进行跨年度对比'}

        # 分析跨年度表现
        comparison_results = {
            'window_days': window_days,
            'target_date': target_date,
            'yearly_data': yearly_performance,
            'yearly_dates': yearly_results,
            'summary': {}
        }

        # 计算各年度的统计信息
        yearly_stats = {}
        all_sectors = set()

        for year, performance in yearly_performance.items():
            sectors_in_year = set(performance['sector_code'].tolist())
            all_sectors.update(sectors_in_year)

            yearly_stats[year] = {
                'total_sectors': len(performance),
                'avg_return': performance['cumulative_return_pct'].mean(),
                'median_return': performance['cumulative_return_pct'].median(),
                'max_return': performance['cumulative_return_pct'].max(),
                'min_return': performance['cumulative_return_pct'].min(),
                'std_return': performance['cumulative_return_pct'].std(),
                'positive_sectors': len(performance[performance['cumulative_return_pct'] > 0]),
                'negative_sectors': len(performance[performance['cumulative_return_pct'] < 0]),
                'top_sector': performance.iloc[0]['sector_name'] if not performance.empty else None,
                'top_return': performance.iloc[0]['cumulative_return_pct'] if not performance.empty else None
            }

        comparison_results['yearly_stats'] = yearly_stats

        # 找出在所有年份都存在的板块，进行一致性对比
        common_sectors = all_sectors
        for year, performance in yearly_performance.items():
            year_sectors = set(performance['sector_code'].tolist())
            common_sectors = common_sectors.intersection(year_sectors)

        if common_sectors:
            sector_comparison = {}
            for sector in common_sectors:
                sector_data = {}
                for year, performance in yearly_performance.items():
                    sector_row = performance[performance['sector_code'] == sector]
                    if not sector_row.empty:
                        sector_data[year] = {
                            'return': sector_row.iloc[0]['cumulative_return_pct'],
                            'rank': sector_row.index[0] + 1,
                            'name': sector_row.iloc[0]['sector_name']
                        }

                if len(sector_data) >= 2:  # 至少在两个年份有数据
                    sector_comparison[sector] = sector_data

            comparison_results['sector_comparison'] = sector_comparison

        self.logger.info(f"跨年度对比完成，涵盖 {len(yearly_performance)} 个年份")
        return comparison_results

    def get_window_summary(self, end_date: Union[str, pd.Timestamp], window_days: int) -> Dict:
        """
        获取时间窗口的摘要信息

        Args:
            end_date: 结束日期
            window_days: 窗口天数

        Returns:
            摘要信息字典
        """
        performance = self.calculate_window_performance(end_date, window_days)

        if performance.empty:
            return {'error': '没有数据'}

        window_dates = self._get_trading_days_in_window(end_date, window_days)

        summary = {
            'window_info': {
                'end_date': end_date,
                'window_days': window_days,
                'actual_trading_days': len(window_dates),
                'start_date': window_dates[0] if window_dates else None,
                'end_date_actual': window_dates[-1] if window_dates else None
            },
            'performance_stats': {
                'total_sectors': len(performance),
                'avg_return': performance['cumulative_return_pct'].mean(),
                'median_return': performance['cumulative_return_pct'].median(),
                'max_return': performance['cumulative_return_pct'].max(),
                'min_return': performance['cumulative_return_pct'].min(),
                'std_return': performance['cumulative_return_pct'].std(),
                'positive_sectors': len(performance[performance['cumulative_return_pct'] > 0]),
                'negative_sectors': len(performance[performance['cumulative_return_pct'] < 0])
            },
            'top_performers': self.get_top_performers(performance, 5).to_dict('records'),
            'bottom_performers': performance.tail(5)[['sector_code', 'sector_name', 'cumulative_return_pct']].to_dict('records')
        }

        return summary

    def find_best_performing_windows(self, sector_code: str, window_days: int, top_n: int = 10) -> pd.DataFrame:
        """
        找出指定板块表现最佳的时间窗口

        Args:
            sector_code: 板块代码
            window_days: 窗口天数
            top_n: 返回前N个时间窗口

        Returns:
            最佳表现时间窗口的DataFrame
        """
        self.logger.info(f"查找板块 {sector_code} 的最佳 {window_days} 日时间窗口")

        # 获取该板块的所有数据
        sector_data = self.data.loc[self.data.index.get_level_values('sector_code') == sector_code]

        if sector_data.empty:
            self.logger.warning(f"没有找到板块 {sector_code} 的数据")
            return pd.DataFrame()

        # 获取该板块的所有日期
        sector_dates = sorted(sector_data.index.get_level_values('date').unique())

        # 计算所有可能的时间窗口表现
        window_results = []

        # 从第window_days个交易日开始，每个日期都作为结束日期计算一次
        for i in range(window_days - 1, len(sector_dates)):
            end_date = sector_dates[i]

            try:
                # 计算这个时间窗口的表现
                performance = self.calculate_window_performance(end_date, window_days)

                # 找到该板块的表现
                sector_performance = performance[performance['sector_code'] == sector_code]

                if not sector_performance.empty:
                    result = sector_performance.iloc[0].copy()
                    window_results.append(result)

            except Exception as e:
                self.logger.debug(f"计算时间窗口 {end_date} 时出错: {str(e)}")
                continue

        if not window_results:
            self.logger.warning(f"没有找到板块 {sector_code} 的有效时间窗口数据")
            return pd.DataFrame()

        # 转换为DataFrame并按表现排序
        results_df = pd.DataFrame(window_results)
        results_df = results_df.sort_values('cumulative_return_pct', ascending=False).head(top_n)
        results_df = results_df.reset_index(drop=True)
        results_df['rank'] = range(1, len(results_df) + 1)

        self.logger.info(f"找到板块 {sector_code} 的 {len(results_df)} 个最佳时间窗口")
        return results_df

    def clear_cache(self):
        """清除缓存"""
        self.cache.clear()
        self.logger.info("时间窗口分析缓存已清除")

    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        return {
            'cache_size': len(self.cache),
            'cached_keys': list(self.cache.keys())
        }
